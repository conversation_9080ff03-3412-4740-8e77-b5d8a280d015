.App {
  text-align: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.App-header {
  background-color: #f5f7fa;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 20px;
  color: #2d3748;
}

h1 {
  color: #2b6cb0;
  margin-bottom: 30px;
}

form {
  width: 100%;
  max-width: 800px;
  background: white;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

fieldset {
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 20px;
  margin-bottom: 20px;
}

legend {
  padding: 0 10px;
  font-weight: bold;
  color: #4a5568;
}

label {
  display: block;
  margin-bottom: 15px;
  text-align: left;
  font-size: 16px;
}

input[type="text"],
input[type="number"],
input[type="date"],
textarea,
input[type="file"] {
  width: 100%;
  padding: 10px;
  margin-top: 5px;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  font-size: 16px;
}

textarea {
  min-height: 100px;
  resize: vertical;
}

button {
  background-color: #4299e1;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.2s;
}

button:hover {
  background-color: #3182ce;
}

.error {
  color: #e53e3e;
  font-size: 14px;
  margin-top: 5px;
}

.success {
  color: #38a169;
  font-size: 16px;
  margin: 20px 0;
}
