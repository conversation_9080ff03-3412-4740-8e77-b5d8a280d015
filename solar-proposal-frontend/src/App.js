import { useState } from 'react';
import * as XLSX from 'xlsx';
import J<PERSON>Z<PERSON> from 'jszip';
import './App.css';

function App() {
  const [formData, setFormData] = useState({
    clientName: '',
    clientLogo: null,
    projectLayout: null,
    location: '',
    annualUnitGoal: '',
    yearsGoal: '',
    date: '',
    systemSize: '',
    co2Reduction: '',
    annualGeneration: '',
    roiYears: '',
    treesSaved: '',
    proposalRef: '',
    projectDescription: '',
    contactPerson: '',
    coordinates: '',
    powerEvacuation: '',
    projectType: '',
    capacity: '',
    moduleCapacity: '',
    annualGHI: '',
    estimatedGeneration: '',
    specificProduction: '',
    epcCostPerWp: '',
    epcCostWithoutTax: '',
    gst: '',
    totalEpcCost: '',
    monthlyConsumption: '',
    solarContribution: '',
    omCost: '',
    omGst: '',
    totalOmCost: '',
    bomFile: null,
    scopeMatrixFile: null,
    roiFile1: null,
    roiFile2: null
  });

  const handleChange = (e) => {
    const { name, value, files } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: files ? files[0] : value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Convert form data to JSON
    const formJson = JSON.stringify(formData, (key, value) => {
      // Skip file objects in the initial JSON
      return value instanceof File ? undefined : value;
    }, 2);

    // Convert Excel files to JSON
    const excelJsons = {};
    const filePromises = [
      { name: 'bomFile', file: formData.bomFile },
      { name: 'scopeMatrixFile', file: formData.scopeMatrixFile },
      { name: 'roiFile1', file: formData.roiFile1 },
      { name: 'roiFile2', file: formData.roiFile2 }
    ].map(async ({ name, file }) => {
      if (!file) return;
      
      const data = await file.arrayBuffer();
      const workbook = XLSX.read(data);
      const worksheet = workbook.Sheets[workbook.SheetNames[0]];
      
      // Get merged cells info
      const merges = worksheet['!merges'] || [];
      
      // Convert to JSON with merged cell info
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
      
      // Process merged cells
      const processedData = jsonData.map((row, rowIndex) => {
        return row.map((cell, colIndex) => {
          // Check if cell is part of a merge
          const merge = merges.find(m =>
            m.s.r <= rowIndex && rowIndex <= m.e.r &&
            m.s.c <= colIndex && colIndex <= m.e.c
          );
          
          if (merge) {
            // Return merged cell value from top-left cell
            return {
              value: jsonData[merge.s.r][merge.s.c],
              isMerged: true,
              mergeRange: {
                startRow: merge.s.r,
                endRow: merge.e.r,
                startCol: merge.s.c,
                endCol: merge.e.c
              }
            };
          }
          return cell;
        });
      });
      
      // Convert back to object format if needed
      if (name === 'bomFile' || name === 'scopeMatrixFile') {
        // Preserve merged cell info for BOM and Scope Matrix
        excelJsons[name] = {
          headers: processedData[0],
          data: processedData.slice(1),
          merges: merges.map(m => ({
            startRow: m.s.r,
            endRow: m.e.r,
            startCol: m.s.c,
            endCol: m.e.c
          }))
        };
      } else {
        // Regular format for ROI files
        excelJsons[name] = XLSX.utils.sheet_to_json(worksheet);
      }
    });

    await Promise.all(filePromises);

    // Combine all JSON data
    const allData = {
      formData: JSON.parse(formJson),
      ...excelJsons
    };

    // Save JSON files to project folder
    try {
      const zip = new JSZip();
      const jsonOutputFolder = zip.folder("json-output");
      
      jsonOutputFolder.file('form-data.json', JSON.stringify(allData.formData, null, 2));
      if (excelJsons.bomFile) jsonOutputFolder.file('bom-data.json', JSON.stringify(excelJsons.bomFile, null, 2));
      if (excelJsons.scopeMatrixFile) jsonOutputFolder.file('scope-data.json', JSON.stringify(excelJsons.scopeMatrixFile, null, 2));
      if (excelJsons.roiFile1) jsonOutputFolder.file('roi1-data.json', JSON.stringify(excelJsons.roiFile1, null, 2));
      if (excelJsons.roiFile2) jsonOutputFolder.file('roi2-data.json', JSON.stringify(excelJsons.roiFile2, null, 2));

      const content = await zip.generateAsync({type:"blob"});
      const url = URL.createObjectURL(content);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'solar-proposal-data.zip';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      alert('JSON files saved in zip folder structure!');
    } catch (error) {
      console.error('Error creating zip:', error);
      alert('Error creating zip file. Please check console for details.');
    }
  };

  return (
    <div className="App">
      <header className="App-header">
        <h1>Solar Energy Proposal System</h1>
        <form onSubmit={handleSubmit}>
          {/* Client Information Section */}
          <fieldset>
            <legend>Client Information</legend>
            <label>
              Client Name:
              <input
                type="text"
                name="clientName"
                value={formData.clientName}
                onChange={handleChange}
                required
              />
            </label>
            <label>
              Client Logo:
              <input
                type="file"
                name="clientLogo"
                onChange={handleChange}
                accept="image/*"
              />
            </label>
            <label>
              Project Layout:
              <input
                type="file"
                name="projectLayout"
                onChange={handleChange}
                accept="image/*"
              />
            </label>
            <label>
              Location:
              <input
                type="text"
                name="location"
                value={formData.location}
                onChange={handleChange}
                required
              />
            </label>
          </fieldset>

          {/* Project Goals Section */}
          <fieldset>
            <legend>Project Goals</legend>
            <label>
              Towards achieving
              <input
               type="text"
               name="annualUnitGoal"
               value={formData.annualUnitGoal}
               onChange={handleChange}
               placeholder="Million"
               required
             />
             Million Annual Unit of clean energy in
             <input
               type="text"
               name="yearsGoal"
               value={formData.yearsGoal}
               onChange={handleChange}
               placeholder="Years"
               required
             />
              years
            </label>
          </fieldset>

          {/* Project Details Section */}
          <fieldset>
            <legend>Project Details</legend>
            <label>
              Date:
              <input
                type="date"
                name="date"
                value={formData.date}
                onChange={handleChange}
                required
              />
            </label>
            <label>
              System Size/Proposed Capacity:
              <input
                type="text"
                name="systemSize"
                value={formData.systemSize}
                onChange={handleChange}
                required
              />
            </label>
            <label>
              CO2 reduction (metric tons/year):
              <input
               type="text"
               name="co2Reduction"
               value={formData.co2Reduction}
               onChange={handleChange}
               required
             />
            </label>
            <label>
              Estimated Annual Generation:
              <input
               type="text"
               name="annualGeneration"
               value={formData.annualGeneration}
               onChange={handleChange}
               required
             />
            </label>
            <label>
              Return of Investment (years):
              <input
               type="text"
               name="roiYears"
               value={formData.roiYears}
               onChange={handleChange}
               required
             />
            </label>
            <label>
              Equivalent Trees Saved:
              <input
               type="text"
               name="treesSaved"
               value={formData.treesSaved}
               onChange={handleChange}
               required
             />
            </label>
            <label>
              Proposal Reference:
              <input
                type="text"
                name="proposalRef"
                value={formData.proposalRef}
                onChange={handleChange}
                required
              />
            </label>
            <label>
              Project Description:
              <textarea
                name="projectDescription"
                value={formData.projectDescription}
                onChange={handleChange}
                required
              />
            </label>
          </fieldset>

          {/* File Uploads Section */}
          <fieldset>
            <legend>File Uploads</legend>
            <label>
              BOM (Bill of Materials) Excel:
              <input
                type="file"
                name="bomFile"
                onChange={handleChange}
                accept=".xlsx,.xls"
                required
              />
            </label>
            <label>
              Scope Matrix Excel:
              <input
                type="file"
                name="scopeMatrixFile"
                onChange={handleChange}
                accept=".xlsx,.xls"
                required
              />
            </label>
            <label>
              ROI Analysis 1 Excel:
              <input
                type="file"
                name="roiFile1"
                onChange={handleChange}
                accept=".xlsx,.xls"
                required
              />
            </label>
            <label>
              ROI Analysis 2 Excel:
              <input
                type="file"
                name="roiFile2"
                onChange={handleChange}
                accept=".xlsx,.xls"
                required
              />
            </label>
          </fieldset>

          {/* Financial Information Section */}
          <fieldset>
            <legend>Financial Information</legend>
            <label>
              Project EPC Cost/Wp:
              <input
               type="text"
               name="epcCostPerWp"
               value={formData.epcCostPerWp}
               onChange={handleChange}
               required
             />
            </label>
            <label>
              Project EPC Cost (without Taxes):
              <input
               type="text"
               name="epcCostWithoutTax"
               value={formData.epcCostWithoutTax}
               onChange={handleChange}
               required
             />
            </label>
            <label>
              GST:
              <input
               type="text"
               name="gst"
               value={formData.gst}
               onChange={handleChange}
               required
             />
            </label>
            <label>
              Total Project EPC Cost (Incl. Taxes):
              <input
               type="text"
               name="totalEpcCost"
               value={formData.totalEpcCost}
               onChange={handleChange}
               required
             />
            </label>
            <label>
              Average Current Monthly Consumption:
              <input
               type="text"
               name="monthlyConsumption"
               value={formData.monthlyConsumption}
               onChange={handleChange}
               required
             />
            </label>
            <label>
              Solar Contribution:
              <input
               type="text"
               name="solarContribution"
               value={formData.solarContribution}
               onChange={handleChange}
               required
             />
            </label>
            <label>
              O&M Cost from 2nd Year:
              <input
               type="text"
               name="omCost"
               value={formData.omCost}
               onChange={handleChange}
               required
             />
            </label>
            <label>
              GST on O&M Cost:
              <input
               type="text"
               name="omGst"
               value={formData.omGst}
               onChange={handleChange}
               required
             />
            </label>
            <label>
              Total O&M Cost:
              <input
               type="text"
               name="totalOmCost"
               value={formData.totalOmCost}
               onChange={handleChange}
               required
             />
            </label>
          </fieldset>

          {/* Technical Specifications */}
          <fieldset>
            <legend>Technical Specifications</legend>
            <label>
              Contact Person:
              <input
                type="text"
                name="contactPerson"
                value={formData.contactPerson}
                onChange={handleChange}
                required
              />
            </label>
            <label>
              Site Coordinates:
              <input
                type="text"
                name="coordinates"
                value={formData.coordinates}
                onChange={handleChange}
                required
                placeholder="Lat, Long"
              />
            </label>
            <label>
              Power Evacuation:
              <input
                type="text"
                name="powerEvacuation"
                value={formData.powerEvacuation}
                onChange={handleChange}
                required
              />
            </label>
            <label>
              Project Type:
              <input
                type="text"
                name="projectType"
                value={formData.projectType}
                onChange={handleChange}
                required
              />
            </label>
            <label>
              Capacity:
              <input
               type="text"
               name="capacity"
               value={formData.capacity}
               onChange={handleChange}
               required
             />
            </label>
            <label>
              Module Capacity:
              <input
               type="text"
               name="moduleCapacity"
               value={formData.moduleCapacity}
               onChange={handleChange}
               required
             />
            </label>
            <label>
              Annual GHI:
              <input
               type="text"
               name="annualGHI"
               value={formData.annualGHI}
               onChange={handleChange}
               required
             />
            </label>
            <label>
              Estimated Generation (kWh/year):
              <input
               type="text"
               name="estimatedGeneration"
               value={formData.estimatedGeneration}
               onChange={handleChange}
               required
             />
            </label>
            <label>
              Specific Production (kWh/kWp/yr):
              <input
               type="text"
               name="specificProduction"
               value={formData.specificProduction}
               onChange={handleChange}
               required
             />
            </label>
          </fieldset>
          <button type="submit">Submit Proposal</button>
        </form>
      </header>
    </div>
  );
}

export default App;
